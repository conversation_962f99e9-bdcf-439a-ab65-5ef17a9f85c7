import { get, post, put } from "@/api";
import { HEADERS_MATERIAL_AI } from "@/api/ServerMap.ts";
import type { PagingParams } from "#/api";
import type { Category } from "@/hooks/useCategory.ts";

export const createFolder = (params: any) => post(`/api/ai/folder`, params, HEADERS_MATERIAL_AI);

export const saveFolder = (data: Category) => put(`/api/ai/folder/${data.id}`, data, HEADERS_MATERIAL_AI);

export const getFolderList = (params: PagingParams<{ ids?: Array<number> }>= {}) => get(`/api/ai/folder/tree`, params, HEADERS_MATERIAL_AI);

export const linkOrganize = (params:any) => 